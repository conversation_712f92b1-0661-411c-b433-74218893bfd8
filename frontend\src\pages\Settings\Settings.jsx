import React, { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Switch, FormControlLabel, Button, Tooltip, CircularProgress, Select, MenuItem, alpha } from "@mui/material";
import axiosInstance from "../../axios";
import { useUser } from "../../hooks/UserHook";
import ConfirmModal from "../../components/ConfirmModal";
import theme from "../../theme";

import { useApp } from "../../hooks/AppHook";
import { datetimeFormats, defaultValues, roles } from "../../utils";
import notificationAlertController from "../../controllers/NotificationAlerts.controller";
import { Info } from "@mui/icons-material";

export default function Settings() {
    const { devMode, setDevMode, showIDs, setShowIDs } = useApp();
    const { user, fetchUser } = useUser();

    const [isTwoFactorAuthEnabled, setIsTwoFactorAuthEnabled] = useState(false);
    const [dateTimeFormat, setDateTimeFormat] = useState(user?.date_time_format);
    const [homePortFilterMode, setHomePortFilterMode] = useState(user?.home_port_filter_mode || "ONLY_NON_HOME_PORTS");
    const [isUseMGRS, setIsUseMGRS] = useState(!!user?.use_MGRS);
    // const [showIDs, setShowIDs] = useState(() => {
    //     const stored = localStorage.getItem("showIDs");
    //     return stored === null ? false : stored === "true";
    // });

    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);
    const [confirmModalDetails, setConfirmModalDetails] = useState({ title: "", message: "" });
    const [isLoading, setIsLoading] = useState(false);

    const openConfirmationDialog = ({ title, message }) => {
        return new Promise((resolve) => {
            const handleConfirm = () => {
                resolve(true);
                setConfirmModalOpen(false);
            };

            const handleCancel = () => {
                resolve(false);
                setConfirmModalOpen(false);
            };

            setConfirmModalDetails({ title, message, onConfirm: handleConfirm, onCancel: handleCancel });
            setConfirmModalOpen(true);
        });
    };

    useEffect(() => {
        if (user?.email_verification_enabled !== undefined) {
            setIsTwoFactorAuthEnabled(user.email_verification_enabled);
        }

        setDateTimeFormat(user?.date_time_format);
        setHomePortFilterMode(user?.home_port_filter_mode || "ONLY_NON_HOME_PORTS");
    }, [user]);

    const toggleDevMode = (value) => {
        setDevMode(value);
        window.location.reload();
    };

    const handleSwitchChange = (event) => {
        setIsTwoFactorAuthEnabled(event.target.checked);
    };

    const handleDateFormatChange = (event) => {
        setDateTimeFormat(event.target.value);
    };
    const handleHomePortChange = (event) => {
        console.log("Home Port Filter Mode Changed", event.target.value);
        setHomePortFilterMode(event.target.value);
    };

    const handleMGRSSwitchChange = (event) => {
        setIsUseMGRS(event.target.checked);
    };

    const toggleShowIDs = (value) => {
        setShowIDs(value);
        localStorage.setItem("showIDs", value);
    };
    const handleSubmit = async () => {
        if (isTwoFactorAuthEnabled !== user?.email_verification_enabled) {
            const confirmed = await openConfirmationDialog({
                title: "Confirm",
                message: (
                    <>
                        Are you sure you want to <b>{isTwoFactorAuthEnabled ? "Enable" : "Disable"}</b> the Two Factor Authentication.
                    </>
                ),
            });

            if (!confirmed) {
                handleCancel();
                return;
            }
            axiosInstance
                .patch("/users/userEmailVerification", null, {
                    params: { email_verification_enabled: isTwoFactorAuthEnabled },
                })
                .then(() => fetchUser().catch(console.error))
                .catch(console.error);
        }

        if (dateTimeFormat !== user?.date_time_format || isUseMGRS !== !!user?.use_MGRS || homePortFilterMode !== user?.home_port_filter_mode) {
            const args = {};

            if (isUseMGRS !== user?.use_MGRS) {
                args["use_MGRS"] = isUseMGRS;
            }

            if (dateTimeFormat !== user?.date_time_format) {
                args["date_time_format"] = dateTimeFormat;
            }
            const isHomePortChanged = homePortFilterMode !== user?.home_port_filter_mode;
            if (isHomePortChanged) {
                args["home_port_filter_mode"] = homePortFilterMode;
            }

            axiosInstance
                .patch("/users/updateSettings", null, {
                    params: args,
                })
                .then(() => {
                    fetchUser().catch(console.error);
                    if (isHomePortChanged) {
                        window.location.reload();
                    }
                })
                .catch(console.error);
        }

        if (showIDs !== (localStorage.getItem("showIDs") === "true")) {
            localStorage.setItem("showIDs", showIDs);
        }
    };

    const handleCancel = () => {
        setIsTwoFactorAuthEnabled(user.email_verification_enabled);
    };

    const isModified = useMemo(
        () =>
            isTwoFactorAuthEnabled !== user?.email_verification_enabled ||
            dateTimeFormat !== user?.date_time_format ||
            isUseMGRS !== !!user?.use_MGRS ||
            homePortFilterMode !== user?.home_port_filter_mode ||
            showIDs !== (localStorage.getItem("showIDs") === "true"),
        [
            isTwoFactorAuthEnabled,
            user?.email_verification_enabled,
            user?.date_time_format,
            dateTimeFormat,
            isUseMGRS,
            user?.use_MGRS,
            homePortFilterMode,
            user?.home_port_filter_mode,
            showIDs,
        ],
    );

    const getLatestNotificationAlerts = async () => {
        setIsLoading(true);
        try {
            const res = await notificationAlertController.getLatestUserNotificationAlerts();
            if (res.status === 200) {
                setIsLoading(false);
            } else {
                setIsLoading(false);
            }
            return res;
        } catch (error) {
            setIsLoading(false);
            console.log("Error Get All Latest Notification Alerts " + error);
            return error.response.data;
        }
    };

    if (!user) {
        return (
            <Grid container justifyContent="center" alignItems="center" height="100%" sx={{ backgroundColor: theme.palette.custom.darkBlue }}>
                <CircularProgress size={30} sx={{ color: "white" }} />
            </Grid>
        );
    }

    return (
        <Grid
            container
            overflow="auto"
            height="100%"
            width="100%"
            flexDirection={{ xs: "row", lg: "column" }}
            paddingX={{ xs: 3, md: 10 }}
            paddingY={{ xs: 5, md: 6 }}
            sx={{
                backgroundColor: theme.palette.custom.darkBlue,
            }}
        >
            <Grid container direction="column" gap="30px">
                <Grid>
                    <Typography variant="h4" component="h1" color="#FFFFFF">
                        Profile Settings
                    </Typography>
                    <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="400">
                        Update your profile.
                    </Typography>
                </Grid>

                <Grid container direction="column" gap="30px">
                    <Grid container paddingBottom="20px" borderBottom={theme.palette.custom.borderColor} borderRadius="10px">
                        <Grid
                            size={{
                                md: 5,
                                sm: 12,
                            }}
                        >
                            <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                                Two Factor Authentication
                            </Typography>
                            <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                                You will have to enter OTP while logging into the account.
                            </Typography>
                        </Grid>
                        <Grid
                            size={{
                                md: "grow",
                                sm: 12,
                            }}
                        >
                            <Tooltip enterDelay={300} title={!user?.email ? "Your Account is not linked with an email" : ""} placement="bottom">
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={isTwoFactorAuthEnabled}
                                            onChange={handleSwitchChange}
                                            disabled={!user?.email}
                                            color="primary"
                                            disableRipple
                                            sx={{
                                                height: "50px",
                                                width: "80px",
                                                borderRadius: "50px",
                                                "& .MuiSwitch-switchBase": {
                                                    padding: "15px 4px",
                                                    transform: "translate(9px, -2px)",
                                                },
                                                "& .MuiSwitch-track": {
                                                    backgroundColor: "#FFFFFF",
                                                    height: "30px",
                                                    borderRadius: "50px",
                                                },
                                                "& .Mui-checked+.MuiSwitch-track": {
                                                    backgroundColor: theme.palette.custom.mainBlue + " !important",
                                                    opacity: "1 !important",
                                                },
                                                "& .Mui-checked.MuiSwitch-switchBase": {
                                                    transform: "translate(36px, -2px)",
                                                },
                                                "& .MuiSwitch-thumb": {
                                                    backgroundColor: "#FFFFFF",
                                                    height: "28px",
                                                    width: "28px",
                                                },
                                                "& .Mui-disabled": {
                                                    opacity: 0.4,
                                                },
                                                "& .Mui-disabled+.MuiSwitch-track": {
                                                    opacity: "0.3 !important",
                                                },
                                            }}
                                        />
                                    }
                                    label="Enable Two Factor Authentication"
                                    sx={{
                                        "& .MuiFormControlLabel-label": {
                                            color: "#FFFFFF !important",
                                            fontSize: "18px",
                                            fontWeight: "400",
                                        },
                                    }}
                                />
                            </Tooltip>
                        </Grid>
                    </Grid>
                    <Grid container paddingBottom="20px" borderBottom={theme.palette.custom.borderColor} borderRadius="10px">
                        <Grid
                            size={{
                                md: 5,
                                sm: 12,
                            }}
                        >
                            <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                                Location Format
                            </Typography>
                            <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                                Choose your preferred location format.
                            </Typography>
                        </Grid>
                        <Grid
                            size={{
                                md: "grow",
                                sm: 12,
                            }}
                        >
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={isUseMGRS}
                                        onChange={handleMGRSSwitchChange}
                                        color="primary"
                                        disableRipple
                                        sx={{
                                            height: "50px",
                                            width: "80px",
                                            borderRadius: "50px",
                                            "& .MuiSwitch-switchBase": {
                                                padding: "15px 4px",
                                                transform: "translate(9px, -2px)",
                                            },
                                            "& .MuiSwitch-track": {
                                                backgroundColor: theme.palette.custom.mainBlue + " !important",
                                                height: "30px",
                                                borderRadius: "50px",
                                                opacity: "1 !important",
                                            },
                                            "& .Mui-checked+.MuiSwitch-track": {
                                                backgroundColor: theme.palette.custom.mainBlue + " !important",
                                                opacity: "1 !important",
                                            },
                                            "& .Mui-checked.MuiSwitch-switchBase": {
                                                transform: "translate(36px, -2px)",
                                            },
                                            "& .MuiSwitch-thumb": {
                                                backgroundColor: "#FFFFFF",
                                                height: "28px",
                                                width: "28px",
                                            },
                                            "& .Mui-disabled": {
                                                opacity: 0.4,
                                            },
                                            "& .Mui-disabled+.MuiSwitch-track": {
                                                opacity: "0.3 !important",
                                            },
                                        }}
                                    />
                                }
                                label={isUseMGRS ? "MGRS" : "Lat/Lng"}
                                sx={{
                                    "& .MuiFormControlLabel-label": {
                                        color: "#FFFFFF !important",
                                        fontSize: "18px",
                                        fontWeight: "400",
                                    },
                                }}
                            />
                        </Grid>
                    </Grid>

                    <Grid container paddingBottom="20px" borderBottom={theme.palette.custom.borderColor} borderRadius="10px">
                        <Grid
                            size={{
                                md: 5,
                                sm: 12,
                            }}
                        >
                            <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                                Date/ Time Format
                            </Typography>
                            <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                                Choose your preferred date/time format.
                            </Typography>
                        </Grid>
                        <Grid
                            size={{
                                md: "grow",
                                sm: 12,
                            }}
                        >
                            <FormControlLabel
                                control={
                                    <Select
                                        labelId="duration-select-label"
                                        id="duration-select"
                                        value={dateTimeFormat ?? Object.keys(datetimeFormats)[0]}
                                        onChange={handleDateFormatChange}
                                        variant={"filled"}
                                    >
                                        {Object.entries(datetimeFormats).map(([format, name]) => (
                                            <MenuItem key={format} value={format}>
                                                {name}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                }
                            />
                        </Grid>
                    </Grid>
                    <Grid container paddingBottom="20px" borderBottom={theme.palette.custom.borderColor} borderRadius="10px">
                        <Grid
                            size={{
                                md: 5,
                                sm: 12,
                            }}
                        >
                            <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                                Home Port filtering
                            </Typography>
                            <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                                View Artifacts that are near or away from home port.
                            </Typography>
                        </Grid>
                        <Grid
                            size={{
                                md: "grow",
                                sm: 12,
                            }}
                        >
                            <FormControlLabel
                                control={
                                    <Select
                                        labelId="duration-select-label"
                                        id="duration-select"
                                        value={homePortFilterMode ?? Object.keys(defaultValues.homePortsFilterModes)[0]}
                                        onChange={handleHomePortChange}
                                        variant={"filled"}
                                    >
                                        {/* eslint-disable-next-line no-unused-vars */}
                                        {Object.entries(defaultValues.homePortsFilterModes).map(([key, value]) => (
                                            <MenuItem key={key} value={key}>
                                                {key}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                }
                            />
                        </Grid>
                    </Grid>
                </Grid>

                <Grid item container flexDirection="column" gap="30px" display={user && user.role_id === roles.super_admin ? "flex" : "none"}>
                    <Grid>
                        <Typography variant="h4" component="h1" color="#FFFFFF">
                            Dashboard Settings
                        </Typography>
                        <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="400">
                            Customize the behavior of the dashboard.
                        </Typography>
                    </Grid>
                </Grid>

                <Grid
                    container
                    display={user && (user.role_id === roles.super_admin || user.role_id === roles.internal_admin) ? "flex" : "none"}
                    paddingBottom="20px"
                    borderBottom={(theme) => theme.palette.custom.borderColor}
                    borderRadius="10px"
                >
                    <Grid
                        size={{
                            md: 5,
                            sm: 12,
                        }}
                    >
                        <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                            Developer Mode
                            <Tooltip
                                enterDelay={300}
                                title="Shows technical details throughout the app, such as stream names and unit IDs on the map, stream pages and other developer related information as well. This mode also unlocks (shows) non-production units on certain screens (i.e. stream sensors list)
                                        In certain cases it might show some new buggy features that are hidden only for devMode (currently none have been hidden).
                                        This option is only available for super admins and internal admins."
                            >
                                <Info
                                    sx={{
                                        color: theme.palette.custom.mediumGrey,
                                        backgroundColor: alpha(theme.palette.custom.offline, 0),
                                        fontSize: "18px",
                                        cursor: "pointer",
                                        marginLeft: "10px",
                                    }}
                                />
                            </Tooltip>
                        </Typography>
                        <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                            View technical details on the dashboard.
                        </Typography>
                    </Grid>
                    <Grid
                        size={{
                            md: "grow",
                            sm: 12,
                        }}
                    >
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={devMode}
                                    onChange={(e) => toggleDevMode(e.target.checked)}
                                    color="primary"
                                    disableRipple
                                    sx={{
                                        height: "50px",
                                        width: "80px",
                                        borderRadius: "50px",
                                        "& .MuiSwitch-switchBase": {
                                            padding: "15px 4px",
                                            transform: "translate(9px, -2px)",
                                        },
                                        "& .MuiSwitch-track": {
                                            backgroundColor: "#FFFFFF",
                                            height: "30px",
                                            borderRadius: "50px",
                                        },
                                        "& .Mui-checked+.MuiSwitch-track": {
                                            backgroundColor: (theme) => theme.palette.custom.mainBlue + " !important",
                                            opacity: "1 !important",
                                        },
                                        "& .Mui-checked.MuiSwitch-switchBase": {
                                            transform: "translate(36px, -2px)",
                                        },
                                        "& .MuiSwitch-thumb": {
                                            backgroundColor: "#FFFFFF",
                                            height: "28px",
                                            width: "28px",
                                        },
                                        "& .Mui-disabled": {
                                            opacity: 0.4,
                                        },
                                        "& .Mui-disabled+.MuiSwitch-track": {
                                            opacity: "0.3 !important",
                                        },
                                    }}
                                />
                            }
                            label="Enable Developer Mode"
                            sx={{
                                "& .MuiFormControlLabel-label": {
                                    color: "#FFFFFF !important",
                                    fontSize: "18px",
                                    fontWeight: "400",
                                },
                            }}
                        />
                    </Grid>
                </Grid>
                {/* Show IDs Toggle */}
                <Grid
                    container
                    paddingBottom="20px"
                    borderBottom={theme.palette.custom.borderColor}
                    borderRadius="10px"
                    display={user && (user.role_id === roles.super_admin || user.role_id === roles.internal_admin) ? "flex" : "none"}
                >
                    <Grid
                        size={{
                            md: 5,
                            sm: 12,
                        }}
                    >
                        <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                            Show IDs
                            <Tooltip
                                enterDelay={300}
                                title="Displays the vessel unit ID alongside the vessel name, but only in the stream vessel list.
                                    This option is only available for super admins and internal admins."
                            >
                                <Info
                                    sx={{
                                        color: theme.palette.custom.mediumGrey,
                                        backgroundColor: alpha(theme.palette.custom.offline, 0.08),
                                        fontSize: "18px",
                                        cursor: "pointer",
                                        marginLeft: "10px",
                                    }}
                                />
                            </Tooltip>
                        </Typography>
                        <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                            Toggle to display Unit IDs in the vessel list.
                        </Typography>
                    </Grid>
                    <Grid
                        size={{
                            md: 5,
                            sm: 12,
                        }}
                    >
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={showIDs}
                                    onChange={(e) => toggleShowIDs(e.target.checked)}
                                    color="primary"
                                    disableRipple
                                    sx={{
                                        height: "50px",
                                        width: "80px",
                                        borderRadius: "50px",
                                        "& .MuiSwitch-switchBase": {
                                            padding: "15px 4px",
                                            transform: "translate(9px, -2px)",
                                        },
                                        "& .MuiSwitch-track": {
                                            backgroundColor: "#FFFFFF",
                                            height: "30px",
                                            borderRadius: "50px",
                                        },
                                        "& .Mui-checked+.MuiSwitch-track": {
                                            backgroundColor: theme.palette.custom.mainBlue + " !important",
                                            opacity: "1 !important",
                                        },
                                        "& .Mui-checked.MuiSwitch-switchBase": {
                                            transform: "translate(36px, -2px)",
                                        },
                                        "& .MuiSwitch-thumb": {
                                            backgroundColor: "#FFFFFF",
                                            height: "28px",
                                            width: "28px",
                                        },
                                        "& .Mui-disabled": {
                                            opacity: 0.4,
                                        },
                                        "& .Mui-disabled+.MuiSwitch-track": {
                                            opacity: "0.3 !important",
                                        },
                                    }}
                                />
                            }
                            label="Enable Show IDs"
                            sx={{
                                "& .MuiFormControlLabel-label": {
                                    color: "#FFFFFF !important",
                                    fontSize: "18px",
                                    fontWeight: "400",
                                },
                            }}
                        />
                    </Grid>
                </Grid>

                <Grid
                    item
                    container
                    display={user && user.permissions.some((p) => p.permission_name === "TEST_NOTIFICATION_ALERTS") ? "flex" : "none"}
                    paddingBottom="20px"
                    borderBottom={(theme) => theme.palette.custom.borderColor}
                    borderRadius="10px"
                >
                    <Grid
                        size={{
                            md: 5,
                            sm: 12,
                        }}
                    >
                        <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                            Get Test Notification Alerts
                            <Tooltip
                                enterDelay={300}
                                title="This is used to verify if the notification system is working correctly. It's mainly for testing the flow from the microservice server.
                                    This will deliver some testing notifications to the user's email.
                                    This option is only available for users with TEST_NOTIFICATION_ALERTS permission."
                            >
                                <Info
                                    sx={{
                                        color: theme.palette.custom.mediumGrey,
                                        backgroundColor: alpha(theme.palette.custom.offline, 0.08),
                                        fontSize: "18px",
                                        cursor: "pointer",
                                        marginLeft: "10px",
                                    }}
                                />
                            </Tooltip>
                        </Typography>
                        <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                            Get the test notification alerts manually by clicking this button.
                        </Typography>
                    </Grid>
                    <Grid
                        size={{
                            md: "grow",
                            sm: 12,
                        }}
                    >
                        <Button
                            variant="contained"
                            onClick={getLatestNotificationAlerts}
                            disabled={isLoading}
                            sx={{
                                color: "#FFFFFF",
                                fontSize: "16px",
                                padding: "5px 20px",
                                borderRadius: "10px",
                                backgroundColor: isLoading ? theme.palette.grey[500] : theme.palette.primary.blue,
                                "&:hover": {
                                    backgroundColor: isLoading ? theme.palette.grey[500] : theme.palette.primary.dark,
                                },
                            }}
                        >
                            {isLoading ? "Loading..." : "Get Alerts"}
                        </Button>
                    </Grid>
                </Grid>

                {isModified && (
                    <Grid
                        container
                        justifyContent="flex-end"
                        alignItems="center"
                        gap={2}
                        sx={{
                            position: "fixed",
                            bottom: 20,
                            right: 20,
                            backgroundColor: alpha(theme.palette.background.paper, 0.95),
                            backdropFilter: "blur(10px)",
                            padding: "12px 20px",
                            borderRadius: "12px",
                            boxShadow: theme.shadows[8],
                            border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                            zIndex: 1300,
                            minWidth: "200px",
                            animation: "slideInUp 0.3s ease-out",
                            "@keyframes slideInUp": {
                                "0%": {
                                    transform: "translateY(100%)",
                                    opacity: 0,
                                },
                                "100%": {
                                    transform: "translateY(0)",
                                    opacity: 1,
                                }
                            }
                        }}
                    >
                        <Button
                            variant="outlined"
                            onClick={handleCancel}
                            sx={{
                                color: "#FFFFFF",
                                borderColor: alpha("#FFFFFF", 0.3),
                                fontSize: "14px",
                                fontWeight: 500,
                                padding: "8px 24px",
                                borderRadius: "8px",
                                textTransform: "none",
                                minWidth: "80px",
                                "&:hover": {
                                    borderColor: "#FFFFFF",
                                    backgroundColor: alpha("#FFFFFF", 0.08),
                                }
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSubmit}
                            variant="contained"
                            sx={{
                                fontSize: "14px",
                                fontWeight: 600,
                                padding: "8px 24px",
                                borderRadius: "8px",
                                textTransform: "none",
                                minWidth: "80px",
                                backgroundColor: theme.palette.custom.mainBlue,
                                color: "#FFFFFF",
                                boxShadow: `0 4px 12px ${alpha(theme.palette.custom.mainBlue, 0.3)}`,
                                "&:hover": {
                                    backgroundColor: "#2c5cc5",
                                    boxShadow: `0 6px 16px ${alpha(theme.palette.custom.mainBlue, 0.4)}`,
                                    transform: "translateY(-1px)",
                                },
                                "&:active": {
                                    transform: "translateY(0)",
                                }
                            }}
                        >
                            Save Changes
                        </Button>
                    </Grid>
                )}
            </Grid>
            <ConfirmModal
                title={confirmModalDetails.title}
                message={confirmModalDetails.message}
                initialState={isConfirmModalOpen}
                onClose={confirmModalDetails.onCancel}
                onConfirm={confirmModalDetails.onConfirm}
            />
        </Grid>
    );
}
